//
//  CHandler.h
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#ifndef C_Dispatcher_Tasker_h
#define C_Dispatcher_Tasker_h

#include "common/common.h"
#include "common/msg.h"

#include "utils/utils.h"
#include "core/ITasker.h"
#include "core/IPlugin.h"

using namespace std;

class CDispatcherTasker : public ITasker
{
public:
    // 构造函数只接收一个路由表，更加清晰
    CDispatcherTasker(map<int, list<IPlugin*>> routing_table);
    virtual ~CDispatcherTasker();
    virtual void* execute(void *msg) override;
    virtual void clear() override;
private:
    // 路由表：从 msgID 映射到订阅了该 msgID 的插件列表
    map<int, list<IPlugin*>> m_routing_table;
};

#endif /* C_Dispatcher_Tasker_h */
