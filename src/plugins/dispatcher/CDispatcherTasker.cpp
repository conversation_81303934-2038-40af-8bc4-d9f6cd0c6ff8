//
//  CDispatcherHandler.cpp
//  pangoo
//
//  Created by <PERSON> on 2023/6/30.
//

#include "CDispatcherTasker.h"
#include "CDispatcherPlugin.h"

CDispatcherTasker::CDispatcherTasker(map<int, list<IPlugin*>> routing_table): m_routing_table(routing_table)
{
    printf("new CDispatcherTasker \n");
}

CDispatcherTasker::~CDispatcherTasker()
{
    printf("delete CDispatcherTasker  \n");
}

void* CDispatcherTasker::execute(void *msg)
{
    if(!msg)
    {
        usleep(100000); // 减少空闲时的休眠时间
        return nullptr;
    }

    ControlPayload *payload = (ControlPayload*)msg;
    
    // 查找是否有插件订阅了这个 msgID
    auto it = m_routing_table.find(payload->msgID);
    if (it != m_routing_table.end())
    {
        // 遍历所有订阅者
        list<IPlugin*>& subscribers = it->second;
        printf("Dispatcher: Routing msgID %d to %zu subscribers.\n", payload->msgID, subscribers.size());
        for(auto& plugin : subscribers)
        {
            // 直接调用 control 接口，传递JSON数据
            plugin->control(payload->msgID, payload->data);
        }
    }
    else
    {
        printf("Dispatcher: No plugin subscribed to msgID %d\n", payload->msgID);
    }

    // Dispatcher 完成任务后，负责释放消息载体
    delete payload;

    return nullptr; // Dispatcher 是消息的终点，不向下游传递
}

void CDispatcherTasker::clear()
{
    return;
}