#!/bin/bash
#
# Nuwa插件控制接口示例脚本
# 演示如何通过命名管道发送控制消息
#

PIPE_PATH="/tmp/pangoo_ipc.pipe"

# 检查管道是否存在
check_pipe() {
    if [ ! -p "$PIPE_PATH" ]; then
        echo "❌ 错误: 命名管道 $PIPE_PATH 不存在"
        echo "请确保Nuwa程序正在运行"
        exit 1
    fi
}

# 发送命令并等待
send_command() {
    local cmd="$1"
    local desc="$2"
    
    echo "🔧 $desc"
    echo "   命令: $cmd"
    echo "$cmd" > "$PIPE_PATH"
    sleep 1
    echo ""
}

echo "🚀 Nuwa插件控制接口示例"
echo "=========================="

check_pipe

echo "📋 1. 获取所有插件状态"
send_command '{"msgID": 301, "data": {"type": "get", "target": "status"}}' "Source插件状态"
send_command '{"msgID": 302, "data": {"type": "get", "target": "status"}}' "Parser插件状态"
send_command '{"msgID": 303, "data": {"type": "get", "target": "status"}}' "Upload插件状态"
send_command '{"msgID": 201, "data": {"type": "get", "target": "status"}}' "Monitor插件状态"

echo "📋 2. 配置Source插件"
send_command '{"msgID": 301, "data": {"type": "set", "filter": "tcp port 80"}}' "设置HTTP过滤器"

echo "📋 3. 配置Parser插件"
send_command '{"msgID": 302, "data": {"type": "set", "debug_mode": true}}' "开启调试模式"
send_command '{"msgID": 302, "data": {"type": "get", "target": "stats"}}' "获取解析统计"

echo "📋 4. 配置Upload插件"
send_command '{"msgID": 303, "data": {"type": "set", "upload_enabled": false}}' "暂时禁用上传"
send_command '{"msgID": 303, "data": {"type": "set", "upload_enabled": true}}' "重新启用上传"

echo "📋 5. 监控操作"
send_command '{"msgID": 201, "data": {"type": "get", "target": "collect_now"}}' "立即收集遥测数据"
send_command '{"msgID": 201, "data": {"type": "get", "target": "cached_data"}}' "查看缓存数据"

echo "📋 6. 广播遥测请求"
send_command '{"msgID": 399, "data": {"type": "get", "target": "telemetry"}}' "获取所有插件遥测数据"

echo "✅ 示例完成！请查看Nuwa程序的日志输出。"
