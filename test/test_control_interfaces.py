#!/usr/bin/env python3
"""
测试所有插件的control接口实现
通过命名管道发送控制消息到各个插件
"""

import os
import time
import json
import sys

PIPE_PATH = "/tmp/pangoo_ipc.pipe"

def send_command(command_dict):
    """发送命令到命名管道"""
    try:
        if not os.path.exists(PIPE_PATH):
            print(f"❌ 错误: 命名管道 '{PIPE_PATH}' 不存在。Nuwa程序是否正在运行？")
            return False

        command_str = json.dumps(command_dict)
        with open(PIPE_PATH, "w") as pipe:
            pipe.write(command_str)
        
        print(f"✅ 成功发送命令: {command_str}")
        return True
    except Exception as e:
        print(f"❌ 发送命令时出错: {e}")
        return False

def test_source_plugin():
    """测试Source插件控制接口"""
    print("\n🔧 测试Source插件控制接口...")
    
    # 获取状态
    send_command({
        "msgID": 301,
        "data": {"type": "get", "target": "status"}
    })
    time.sleep(0.5)
    
    # 设置过滤器
    send_command({
        "msgID": 301,
        "data": {"type": "set", "filter": "tcp port 80 or tcp port 443"}
    })
    time.sleep(0.5)

def test_parser_plugin():
    """测试Parser插件控制接口"""
    print("\n🔧 测试Parser插件控制接口...")
    
    # 获取状态
    send_command({
        "msgID": 302,
        "data": {"type": "get", "target": "status"}
    })
    time.sleep(0.5)
    
    # 开启调试模式
    send_command({
        "msgID": 302,
        "data": {"type": "set", "debug_mode": True}
    })
    time.sleep(0.5)
    
    # 获取统计信息
    send_command({
        "msgID": 302,
        "data": {"type": "get", "target": "stats"}
    })
    time.sleep(0.5)
    
    # 重置统计信息
    send_command({
        "msgID": 302,
        "data": {"type": "set", "reset_stats": True}
    })
    time.sleep(0.5)

def test_upload_plugin():
    """测试Upload插件控制接口"""
    print("\n🔧 测试Upload插件控制接口...")
    
    # 获取状态
    send_command({
        "msgID": 303,
        "data": {"type": "get", "target": "status"}
    })
    time.sleep(0.5)
    
    # 禁用上传功能
    send_command({
        "msgID": 303,
        "data": {"type": "set", "upload_enabled": False}
    })
    time.sleep(0.5)
    
    # 重新启用上传功能
    send_command({
        "msgID": 303,
        "data": {"type": "set", "upload_enabled": True}
    })
    time.sleep(0.5)
    
    # 重置统计信息
    send_command({
        "msgID": 303,
        "data": {"type": "set", "reset_stats": True}
    })
    time.sleep(0.5)

def test_monitor_plugin():
    """测试Monitor插件控制接口"""
    print("\n🔧 测试Monitor插件控制接口...")
    
    # 获取状态
    send_command({
        "msgID": 201,
        "data": {"type": "get", "target": "status"}
    })
    time.sleep(0.5)
    
    # 立即收集遥测数据
    send_command({
        "msgID": 201,
        "data": {"type": "get", "target": "collect_now"}
    })
    time.sleep(0.5)
    
    # 获取缓存数据
    send_command({
        "msgID": 201,
        "data": {"type": "get", "target": "cached_data"}
    })
    time.sleep(0.5)
    
    # 清空缓存
    send_command({
        "msgID": 201,
        "data": {"type": "set", "clear_cache": True}
    })
    time.sleep(0.5)

def test_broadcast_telemetry():
    """测试广播遥测请求"""
    print("\n🔧 测试广播遥测请求...")
    
    send_command({
        "msgID": 399,
        "data": {"type": "get", "target": "telemetry"}
    })
    time.sleep(1.0)

def main():
    print("🚀 Nuwa插件控制接口测试工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        plugin = sys.argv[1].lower()
        if plugin == "source":
            test_source_plugin()
        elif plugin == "parser":
            test_parser_plugin()
        elif plugin == "upload":
            test_upload_plugin()
        elif plugin == "monitor":
            test_monitor_plugin()
        elif plugin == "telemetry":
            test_broadcast_telemetry()
        else:
            print(f"❌ 未知的插件: {plugin}")
            print("可用选项: source, parser, upload, monitor, telemetry")
            return 1
    else:
        # 测试所有插件
        test_source_plugin()
        test_parser_plugin()
        test_upload_plugin()
        test_monitor_plugin()
        test_broadcast_telemetry()
    
    print("\n✅ 测试完成！请查看Nuwa程序的日志输出以确认控制消息是否正确处理。")
    return 0

if __name__ == "__main__":
    sys.exit(main())
