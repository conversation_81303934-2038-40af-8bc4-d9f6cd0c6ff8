#!/usr/bin/env python3
"""
简化版本的共享内存消费者 - 用于测试和调试
"""

import os
import sys
import time
import json
import struct
from pathlib import Path

def find_shared_memory():
    """查找共享内存文件"""
    possible_paths = [
        "/tmp/nuwa_upload_queue.shm",
        "/tmp/nuwa_upload_queue",
        "/dev/shm/nuwa_upload_queue",
        "/var/tmp/nuwa_upload_queue"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ Found shared memory at: {path}")
            return path
    
    print("❌ Shared memory not found in any of these locations:")
    for path in possible_paths:
        print(f"  - {path}")
    
    # 列出可能的共享内存文件
    print("\n🔍 Searching for shared memory files...")
    for directory in ["/tmp", "/dev/shm", "/var/tmp"]:
        if os.path.exists(directory):
            try:
                files = [f for f in os.listdir(directory) if "nuwa" in f.lower()]
                if files:
                    print(f"  Found in {directory}: {files}")
            except PermissionError:
                print(f"  Cannot access {directory}")
    
    return None

def read_shared_memory_raw(path):
    """读取共享内存的原始数据"""
    try:
        with open(path, 'rb') as f:
            # 读取前64字节的头部信息
            header = f.read(64)
            print(f"📊 Header (first 64 bytes): {header.hex()}")
            
            # 尝试解析前16字节作为两个size_t
            if len(header) >= 16:
                producer_idx = struct.unpack('Q', header[0:8])[0]
                consumer_idx = struct.unpack('Q', header[8:16])[0]
                print(f"📈 Producer index: {producer_idx}")
                print(f"📉 Consumer index: {consumer_idx}")
                
                if producer_idx != consumer_idx:
                    print(f"🎉 Queue has {producer_idx - consumer_idx} items!")
                    
                    # 尝试读取第一个条目
                    entry_offset = 16  # 跳过头部
                    f.seek(entry_offset)
                    
                    # 读取消息长度
                    msg_len_data = f.read(8)
                    if len(msg_len_data) == 8:
                        msg_len = struct.unpack('Q', msg_len_data)[0]
                        print(f"📏 First message length: {msg_len}")
                        
                        if 0 < msg_len <= 4096:  # 合理的消息长度
                            msg_data = f.read(msg_len)
                            if len(msg_data) == msg_len:
                                try:
                                    message = msg_data.decode('utf-8')
                                    print(f"📝 First message: {message[:200]}...")
                                    
                                    # 尝试解析JSON
                                    try:
                                        json_data = json.loads(message)
                                        print(f"✅ Valid JSON found!")
                                        print(f"🔥 Event: {json_data.get('event_type', 'unknown')}")
                                        print(f"🌐 URL: {json_data.get('http', {}).get('url', 'unknown')}")
                                        return True
                                    except json.JSONDecodeError as e:
                                        print(f"❌ JSON parse error: {e}")
                                except UnicodeDecodeError as e:
                                    print(f"❌ UTF-8 decode error: {e}")
                else:
                    print("📭 Queue is empty")
            
    except Exception as e:
        print(f"❌ Error reading shared memory: {e}")
    
    return False

def main():
    print("🔍 Nuwa Shared Memory Inspector")
    print("=" * 50)
    
    # 查找共享内存
    shm_path = find_shared_memory()
    if not shm_path:
        print("\n⏳ Waiting for shared memory to be created...")
        for i in range(30):
            time.sleep(1)
            shm_path = find_shared_memory()
            if shm_path:
                break
            if i % 5 == 4:
                print(f"  Still waiting... ({i+1}/30)")
        
        if not shm_path:
            print("❌ Timeout waiting for shared memory")
            return 1
    
    print(f"\n📂 Shared memory file: {shm_path}")
    
    # 获取文件信息
    try:
        stat = os.stat(shm_path)
        print(f"📏 File size: {stat.st_size} bytes")
        print(f"🕐 Modified: {time.ctime(stat.st_mtime)}")
    except Exception as e:
        print(f"❌ Cannot get file stats: {e}")
        return 1
    
    # 读取共享内存数据
    print(f"\n📖 Reading shared memory data...")
    success = read_shared_memory_raw(shm_path)
    
    if success:
        print(f"\n🎉 Successfully found and parsed data!")
    else:
        print(f"\n⚠️  No valid data found, but shared memory exists")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
