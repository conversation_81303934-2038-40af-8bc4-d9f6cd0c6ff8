# Nuwa 共享内存数据消费者

本文档介绍如何使用共享内存和无锁队列从Nuwa Upload插件中消费HTTP解析数据。

## 架构概述

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Source Plugin │───▶│   Parser Plugin  │───▶│  Upload Plugin  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
                                               ┌─────────────────┐
                                               │ Shared Memory   │
                                               │ SPSC Queue      │
                                               └─────────────────┘
                                                         │
                                                         ▼
                                               ┌─────────────────┐
                                               │ Consumer Apps   │
                                               │ (Rust/Python)  │
                                               └─────────────────┘
```

## 技术特性

- **SPSC无锁队列**: Single Producer Single Consumer，高性能无锁设计
- **共享内存**: 使用POSIX共享内存实现跨进程通信
- **原子操作**: 使用原子索引确保线程安全
- **环形缓冲区**: 固定大小的环形缓冲区，避免内存碎片
- **跨语言支持**: 提供Rust和Python两种消费者实现

## 配置参数

```cpp
constexpr size_t QUEUE_SIZE = 1024;           // 队列容量（条目数）
constexpr size_t MAX_MESSAGE_SIZE = 4096;     // 单条消息最大大小
```

## 数据结构

### 队列头部
```cpp
struct QueueHeader {
    std::atomic<size_t> producer_index{0};     // 生产者索引
    std::atomic<size_t> consumer_index{0};     // 消费者索引
};
```

### 队列条目
```cpp
struct QueueEntry {
    size_t message_length;                     // 消息长度
    char message_data[MAX_MESSAGE_SIZE];       // 消息数据
};
```

## 使用方法

### 1. 构建Nuwa项目

```bash
cd build
ninja
```

### 2. 构建Rust消费者

```bash
cd test/consumer
./build.sh
```

### 3. 运行消费者程序

#### Rust版本
```bash
# 基本使用
./target/release/consumer

# 详细模式
./target/release/consumer -d

# 自定义轮询间隔（50ms）
./target/release/consumer -i 50

# 处理最多100个事件
./target/release/consumer -m 100

# 静默模式
./target/release/consumer -q
```

#### Python版本
```bash
# 基本使用
python3 test/consumer_python/consumer.py

# 详细模式
python3 test/consumer_python/consumer.py -d

# 自定义轮询间隔（50ms）
python3 test/consumer_python/consumer.py -i 50
```

### 4. 运行Nuwa主程序

```bash
cd build/deploy
./pangoo
```

### 5. 生成HTTP流量进行测试

```bash
# 在另一个终端中生成HTTP请求
curl http://www.baidu.com
curl http://www.google.com
curl http://httpbin.org/get
```

## 输出示例

### 简洁模式输出
```
🚀 Nuwa HTTP Data Consumer Starting...
📊 Polling interval: 100ms
📋 Detailed mode: OFF
🎯 Max events: Unlimited
────────────────────────────────────────────────────────────
✅ Connected to shared memory queue

🔥 Event #1
[2025-08-06 20:45:12.123] GET http://www.baidu.com/ -> 220.181.38.148:80 | 0 | Complete
────────────────────────────────────────────────────────────

🔥 Event #2
[2025-08-06 20:45:15.456] GET http://www.google.com/ -> 142.250.191.14:80 | 0 | Complete
────────────────────────────────────────────────────────────
```

### 详细模式输出
```
🔥 Event #1
Event ID: evt_1754483912123_1234
Timestamp: 1754483912
Capture Time: 2025-08-06 20:45:12.123
HTTP Method: GET
URL: http://www.baidu.com/
Path: /
Query: 
Version: HTTP/1.1
Status: 0 
Content Length: 0
Complete: true
Source: 192.168.1.100:54321
Destination: 220.181.38.148:80
Protocol: TCP
Parsed by: nuwa_parser
Uploaded by: nuwa_uploader
Valid: true
```

## 故障排除

### 1. 共享内存连接失败
```
⏳ Waiting for shared memory queue... Failed to open shared memory
```
**解决方案**: 确保Nuwa主程序已启动，Upload插件会自动创建共享内存。

### 2. 权限问题
```
Failed to create shared memory: Permission denied
```
**解决方案**: 检查共享内存权限，或以适当权限运行程序。

### 3. 队列已满
```
CUploadTasker: 写入共享内存失败
```
**解决方案**: 增加消费者处理速度，或增大队列大小。

## 性能优化

1. **调整轮询间隔**: 根据数据量调整消费者轮询间隔
2. **批量处理**: 在消费者中实现批量处理逻辑
3. **多消费者**: 可以运行多个消费者实例（需要修改为MPSC模式）
4. **内存预分配**: 避免频繁的内存分配

## 扩展功能

1. **数据持久化**: 将消费的数据保存到数据库或文件
2. **实时分析**: 实现实时HTTP流量分析
3. **告警系统**: 基于特定条件触发告警
4. **Web界面**: 提供Web界面展示实时数据
5. **指标收集**: 收集性能指标和统计信息

## 注意事项

1. **内存管理**: 共享内存由生产者（Upload插件）负责创建和清理
2. **数据一致性**: 使用原子操作确保数据一致性
3. **错误处理**: 消费者应该能够处理格式错误的数据
4. **资源清理**: 程序退出时正确清理资源
5. **平台兼容性**: 当前实现主要针对Unix-like系统
